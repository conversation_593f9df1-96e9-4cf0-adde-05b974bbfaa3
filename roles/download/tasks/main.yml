---
- name: Download | Prepare working directories and variables
  import_tasks: prep_download.yml
  when:
    - not skip_downloads | default(false)
  tags:
    - download
    - upload

- name: Download | Get kubeadm binary and list of required images
  include_tasks: prep_kubeadm_images.yml
  when:
    - not skip_downloads | default(false)
    - ('kube_control_plane' in group_names)
  tags:
    - download
    - upload

- name: Download | Download files / images
  include_tasks: "{{ include_file }}"
  loop: "{{ downloads | combine(kubeadm_images) | dict2items }}"
  vars:
    download: "{{ download_defaults | combine(item.value) }}"
    include_file: "download_{% if download.container %}container{% else %}file{% endif %}.yml"
  when:
    - not skip_downloads | default(false)
    - download.enabled
    - item.value.enabled
    - (not (item.value.container | default(false))) or (item.value.container and download_container)
    - (download_run_once and inventory_hostname == download_delegate) or (group_names | intersect(download.groups) | length)
