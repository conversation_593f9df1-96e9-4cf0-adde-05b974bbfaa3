---
# The image_info_command depends on the Container Runtime and will output something like the following:
# nginx:1.15,gcr.io/google-containers/kube-proxy:v1.14.1,gcr.io/google-containers/kube-proxy@sha256:44af2833c6cbd9a7fc2e9d2f5244a39dfd2e31ad91bf9d4b7d810678db738ee9,gcr.io/google-containers/kube-apiserver:v1.14.1,etc...
- name: Check_pull_required |  Generate a list of information about the images on a node  # noqa command-instead-of-shell - image_info_command contains a pipe, therefore requiring shell
  shell: "{{ image_info_command }}"
  register: docker_images
  changed_when: false
  check_mode: false
  when: not download_always_pull

- name: Check_pull_required | Set pull_required if the desired image is not yet loaded
  set_fact:
    pull_required: >-
      {%- if image_reponame | regex_replace('^docker\.io/(library/)?', '') in docker_images.stdout.split(',') %}false{%- else -%}true{%- endif -%}
  when: not download_always_pull

- name: Check_pull_required | Check that the local digest sha256 corresponds to the given image tag
  assert:
    that: "{{ download.repo }}:{{ download.tag }} in docker_images.stdout.split(',')"
  when:
    - not download_always_pull
    - not pull_required
    - pull_by_digest
  tags:
    - asserts
