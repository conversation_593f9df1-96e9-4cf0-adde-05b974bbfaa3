---
- name: Prep_download | Set a few facts
  set_fact:
    download_force_cache: "{{ true if download_run_once else download_force_cache }}"
  tags:
    - facts

- name: Prep_download | On localhost, check if passwordless root is possible
  command: "true"
  delegate_to: localhost
  connection: local
  run_once: true
  register: test_become
  changed_when: false
  ignore_errors: true  # noqa ignore-errors
  become: true
  when:
    - download_localhost
  tags:
    - localhost
    - asserts

- name: Prep_download | On localhost, check if user has access to the container runtime without using sudo
  shell: "{{ image_info_command_on_localhost }}"  # noqa command-instead-of-shell - image_info_command_on_localhost contains pipe, therefore requires shell
  delegate_to: localhost
  connection: local
  run_once: true
  register: test_docker
  changed_when: false
  ignore_errors: true  # noqa ignore-errors
  become: false
  when:
    - download_localhost
  tags:
    - localhost
    - asserts

- name: Prep_download | Parse the outputs of the previous commands
  set_fact:
    user_in_docker_group: "{{ not test_docker.failed }}"
    user_can_become_root: "{{ not test_become.failed }}"
  when:
    - download_localhost
  tags:
    - localhost
    - asserts

- name: Prep_download | Check that local user is in group or can become root
  assert:
    that: "user_in_docker_group or user_can_become_root"
    msg: >-
      Error: User is not in docker group and cannot become root. When download_localhost is true, at least one of these two conditions must be met.
  when:
    - download_localhost
  tags:
    - localhost
    - asserts

- name: Prep_download | Register docker images info
  shell: "{{ image_info_command }}"  # noqa command-instead-of-shell - image_info_command contains pipe therefore requires shell
  no_log: "{{ not (unsafe_show_logs | bool) }}"
  register: docker_images
  failed_when: false
  changed_when: false
  check_mode: false
  when: download_container

- name: Prep_download | Create staging directory on remote node
  file:
    path: "{{ local_release_dir }}/images"
    state: directory
    mode: "0755"
    owner: "{{ ansible_ssh_user | default(ansible_user_id) }}"
  when:
    - ansible_os_family not in ["Flatcar", "Flatcar Container Linux by Kinvolk"]

- name: Prep_download | Create local cache for files and images on control node
  file:
    path: "{{ download_cache_dir }}/images"
    state: directory
    mode: "0755"
  delegate_to: localhost
  connection: local
  delegate_facts: false
  run_once: true
  become: false
  when:
    - download_force_cache
  tags:
    - localhost
