---
- name: "Download_file | download {{ download.dest }}"
  tags:
  - download
  block:
  - name: Prep_download | Set a few facts
    set_fact:
      download_force_cache: "{{ true if download_run_once else download_force_cache }}"

  - name: Download_file | Show url of file to download
    when: unsafe_show_logs | bool
    debug:
      msg: "{{ download.url }}"
    run_once: "{{ download_run_once }}"

  - name: Download_file | Set pathname of cached file
    set_fact:
      file_path_cached: "{{ download_cache_dir }}/{{ download.dest | basename }}"
    tags:
    - facts

  - name: Download_file | Create dest directory on node
    file:
      path: "{{ download.dest | dirname }}"
      owner: "{{ download.owner | default(omit) }}"
      mode: "0755"
      state: directory
      recurse: true

  - name: Download_file | Create local cache directory
    file:
      path: "{{ file_path_cached | dirname }}"
      state: directory
      recurse: true
    delegate_to: localhost
    connection: local
    delegate_facts: false
    run_once: true
    become: false
    when:
    - download_force_cache
    tags:
    - localhost

  - name: Download_file | Create cache directory on download_delegate host
    file:
      path: "{{ file_path_cached | dirname }}"
      state: directory
      recurse: true
    delegate_to: "{{ download_delegate }}"
    delegate_facts: false
    run_once: true
    when:
    - download_force_cache
    - not download_localhost

  # This must always be called, to check if the checksum matches. On no-match the file is re-downloaded.
  # This task will avoid logging it's parameters to not leak environment passwords in the log
  - name: Download_file | Download item
    get_url:
      url: "{{ download.url }}"
      dest: "{{ file_path_cached if download_force_cache else download.dest }}"
      owner: "{{ omit if download_localhost else (download.owner | default(omit)) }}"
      mode: "{{ omit if download_localhost else (download.mode | default(omit)) }}"
      checksum: "{{ download.checksum }}"
      validate_certs: "{{ download_validate_certs }}"
      url_username: "{{ download.username | default(omit) }}"
      url_password: "{{ download.password | default(omit) }}"
      force_basic_auth: "{{ download.force_basic_auth | default(omit) }}"
      timeout: "{{ download.timeout | default(omit) }}"
    delegate_to: "{{ download_delegate if download_force_cache else inventory_hostname }}"
    run_once: "{{ download_force_cache }}"
    register: get_url_result
    become: "{{ not download_localhost }}"
    until: "'OK' in get_url_result.msg or
      'file already exists' in get_url_result.msg or
      get_url_result.status_code | default() == 304"
    retries: "{{ download_retries }}"
    delay: "{{ retry_stagger | default(5) }}"
    environment: "{{ proxy_env }}"
    no_log: "{{ not (unsafe_show_logs | bool) }}"

  - name: Download_file | Copy file back to ansible host file cache
    ansible.posix.synchronize:
      src: "{{ file_path_cached }}"
      dest: "{{ file_path_cached }}"
      use_ssh_args: true
      mode: pull
    when:
    - download_force_cache
    - not download_localhost
    - download_delegate == inventory_hostname

  - name: Download_file | Copy file from cache to nodes, if it is available
    ansible.posix.synchronize:
      src: "{{ file_path_cached }}"
      dest: "{{ download.dest }}"
      use_ssh_args: true
      mode: push
    register: get_task
    until: get_task is succeeded
    delay: "{{ retry_stagger | random + 3 }}"
    retries: "{{ download_retries }}"
    when:
    - download_force_cache

  - name: Download_file | Set mode and owner
    file:
      path: "{{ download.dest }}"
      mode: "{{ download.mode | default(omit) }}"
      owner: "{{ download.owner | default(omit) }}"
    when:
    - download_force_cache

  - name: "Download_file | Extract file archives"
    include_tasks: "extract_file.yml"
