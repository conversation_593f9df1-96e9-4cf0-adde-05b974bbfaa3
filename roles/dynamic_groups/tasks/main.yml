---
- name: Match needed groups by their old names or definition
  vars:
    group_mappings:
      kube_control_plane:
        - kube-master
      kube_node:
        - kube-node
      calico_rr:
        - calico-rr
      no_floating:
        - no-floating
      k8s_cluster:
        - kube_node
        - kube_control_plane
        - calico_rr
  group_by:
    key: "{{ (group_names | intersect(item.value) | length > 0) | ternary(item.key, '_all') }}"
  loop: "{{ group_mappings | dict2items }}"
