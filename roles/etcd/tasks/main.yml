---
- name: Check etcd certs
  include_tasks: check_certs.yml
  when: cert_management == "script"
  tags:
    - etcd-secrets
    - facts

- name: Generate etcd certs
  include_tasks: "gen_certs_script.yml"
  when:
    - cert_management == "script"
  tags:
    - etcd-secrets

- name: Trust etcd CA
  include_tasks: upd_ca_trust.yml
  when:
    - ('etcd' in group_names) or ('kube_control_plane' in group_names)
  tags:
    - etcd-secrets

- name: Trust etcd CA on nodes if needed
  include_tasks: upd_ca_trust.yml
  when:
    - kube_network_plugin in ["calico", "flannel", "cilium"] or cilium_deploy_additionally
    - kube_network_plugin != "calico" or calico_datastore == "etcd"
    - ('k8s_cluster' in group_names)
  tags:
    - etcd-secrets

- name: "Gen_certs | Get etcd certificate serials"
  command: "openssl x509 -in {{ etcd_cert_dir }}/node-{{ inventory_hostname }}.pem -noout -serial"
  register: "etcd_client_cert_serial_result"
  changed_when: false
  check_mode: false
  when:
    - kube_network_plugin in ["calico", "flannel", "cilium"] or cilium_deploy_additionally
    - kube_network_plugin != "calico" or calico_datastore == "etcd"
    - ('k8s_cluster' in group_names)
  tags:
    - control-plane
    - network

- name: Set etcd_client_cert_serial
  set_fact:
    etcd_client_cert_serial: "{{ etcd_client_cert_serial_result.stdout.split('=')[1] }}"
  when:
    - kube_network_plugin in ["calico", "flannel", "cilium"] or cilium_deploy_additionally
    - kube_network_plugin != "calico" or calico_datastore == "etcd"
    - ('k8s_cluster' in group_names)
  tags:
    - control-plane
    - network

- name: Install etcdctl and etcdutl binary
  import_role:
    name: etcdctl_etcdutl
  tags:
    - etcdctl
    - etcdutl
    - upgrade
  when:
    - ('etcd' in group_names)
    - etcd_cluster_setup

- name: Install etcd
  include_tasks: "install_{{ etcd_deployment_type }}.yml"
  when: ('etcd' in group_names)
  tags:
    - upgrade

- name: Configure etcd
  include_tasks: configure.yml
  when: ('etcd' in group_names)

- name: Refresh etcd config
  include_tasks: refresh_config.yml
  when: ('etcd' in group_names)

- name: Restart etcd if certs changed
  command: /bin/true
  notify: Restart etcd
  when:
    - ('etcd' in group_names)
    - etcd_cluster_setup
    - etcd_secret_changed | default(false)

- name: Restart etcd-events if certs changed
  command: /bin/true
  notify: Restart etcd
  when:
    - ('etcd' in group_names)
    - etcd_events_cluster_setup
    - etcd_secret_changed | default(false)

# After etcd cluster is assembled, make sure that
# initial state of the cluster is in `existing`
# state instead of `new`.
- name: Refresh etcd config again for idempotency
  include_tasks: refresh_config.yml
  when: ('etcd' in group_names)
