---
- name: Join Member | Add member to etcd cluster
  command: "{{ bin_dir }}/etcdctl member add {{ etcd_member_name }} --peer-urls={{ etcd_peer_url }}"
  register: member_add_result
  until: member_add_result.rc == 0 or 'Peer URLs already exists' in member_add_result.stderr
  failed_when: member_add_result.rc != 0 and 'Peer URLs already exists' not in member_add_result.stderr
  retries: "{{ etcd_retries }}"
  delay: "{{ retry_stagger | random + 3 }}"
  environment:
    ETCDCTL_API: "3"
    ETCDCTL_CERT: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}.pem"
    ETCDCTL_KEY: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}-key.pem"
    ETCDCTL_CACERT: "{{ etcd_cert_dir }}/ca.pem"
    ETCDCTL_ENDPOINTS: "{{ etcd_access_addresses }}"

- name: Join Member | Refresh etcd config
  include_tasks: refresh_config.yml
  vars:
    # noqa: jinja[spacing]
    etcd_peer_addresses: >-
      {% for host in groups['etcd'] -%}
        {%- if hostvars[host]['etcd_member_in_cluster'].rc == 0 -%}
          {{ "etcd" + loop.index | string }}=https://{{ hostvars[host].etcd_access_address | default(hostvars[host]['main_ip']) | ansible.utils.ipwrap }}:2380,
        {%- endif -%}
        {%- if loop.last -%}
          {{ etcd_member_name }}={{ etcd_peer_url }}
        {%- endif -%}
      {%- endfor -%}

- name: Join Member | Ensure member is in etcd cluster
  shell: "set -o pipefail && {{ bin_dir }}/etcdctl member list | grep -w {{ etcd_access_address }} >/dev/null"
  args:
    executable: /bin/bash
  register: etcd_member_in_cluster
  changed_when: false
  check_mode: false
  retries: "{{ etcd_retries }}"
  delay: "{{ retry_stagger | random + 3 }}"
  until: etcd_member_in_cluster.rc == 0
  tags:
    - facts
  environment:
    ETCDCTL_API: "3"
    ETCDCTL_CERT: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}.pem"
    ETCDCTL_KEY: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}-key.pem"
    ETCDCTL_CACERT: "{{ etcd_cert_dir }}/ca.pem"
    ETCDCTL_ENDPOINTS: "{{ etcd_access_addresses }}"

- name: Configure | Ensure etcd is running
  service:
    name: etcd
    state: started
    enabled: true
