---
- name: Refresh Time Fact
  setup:
    filter: ansible_date_time
  listen: Restart etcd
  when: etcd_cluster_is_healthy.rc == 0

- name: Set Backup Directory
  set_fact:
    etcd_backup_directory: "{{ etcd_backup_prefix }}/etcd-{{ ansible_date_time.date }}_{{ ansible_date_time.time }}"
  listen: Restart etcd

- name: Create Backup Directory
  file:
    path: "{{ etcd_backup_directory }}"
    state: directory
    owner: root
    group: root
    mode: "0600"
  listen: Restart etcd
  when: etcd_cluster_is_healthy.rc == 0

- name: Stat etcd v2 data directory
  stat:
    path: "{{ etcd_data_dir }}/member"
    get_attributes: false
    get_checksum: false
    get_mime: false
  register: etcd_data_dir_member
  listen: Restart etcd
  when: etcd_cluster_is_healthy.rc == 0

- name: Backup etcd v2 data
  when:
    - etcd_data_dir_member.stat.exists
    - etcd_cluster_is_healthy.rc == 0
  command: >-
    {{ bin_dir }}/etcdctl backup
      --data-dir {{ etcd_data_dir }}
      --backup-dir {{ etcd_backup_directory }}
  environment:
    ETCDCTL_API: "2"
  retries: 3
  register: backup_v2_command
  until: backup_v2_command.rc == 0
  delay: "{{ retry_stagger | random + 3 }}"
  listen: Restart etcd

- name: Backup etcd v3 data
  command: >-
    {{ bin_dir }}/etcdctl
      snapshot save {{ etcd_backup_directory }}/snapshot.db
  environment:
    ETCDCTL_API: "3"
    ETCDCTL_ENDPOINTS: "{{ etcd_access_addresses.split(',') | first }}"
    ETCDCTL_CERT: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}.pem"
    ETCDCTL_KEY: "{{ etcd_cert_dir }}/admin-{{ inventory_hostname }}-key.pem"
    ETCDCTL_CACERT: "{{ etcd_cert_dir }}/ca.pem"
  retries: 3
  register: etcd_backup_v3_command
  until: etcd_backup_v3_command.rc == 0
  delay: "{{ retry_stagger | random + 3 }}"
  listen: Restart etcd
  when: etcd_cluster_is_healthy.rc == 0
