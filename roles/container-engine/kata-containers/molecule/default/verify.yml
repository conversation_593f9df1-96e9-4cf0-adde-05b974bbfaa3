---
- name: Test kata-containers
  hosts: all
  gather_facts: false
  tasks:
  - name: Test version
    command: "/opt/kata/bin/kata-runtime version"
    register: version
    failed_when: >
      version is failed or
      'kata-runtime' not in version.stdout
  - name: Test version
    command: "/opt/kata/bin/kata-runtime check"
    register: check
    failed_when: >
      check is failed or
      'System is capable of running' not in check.stdout

- name: Test run container
  import_playbook: ../../../molecule/test_runtime.yml
  vars:
    container_runtime: kata-qemu
    container_manager: containerd
