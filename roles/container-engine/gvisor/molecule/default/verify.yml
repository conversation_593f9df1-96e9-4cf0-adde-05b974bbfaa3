---
- name: Test gvisor
  hosts: all
  gather_facts: false
  tasks:
  - name: Get <PERSON>ubespray defaults
    import_role:
      name: ../../../../../kubespray_defaults
  - name: Test version
    command: "{{ bin_dir }}/runsc --version"
    register: runsc_version
    failed_when: >
      runsc_version is failed or
      'runsc version' not in runsc_version.stdout

- name: Test run container
  import_playbook: ../../../molecule/test_runtime.yml
  vars:
    container_runtime: runsc
