---
- name: GVisor | Download runsc binary
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.gvisor_runsc) }}"

- name: GVisor | Download containerd-shim-runsc-v1 binary
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.gvisor_containerd_shim) }}"

- name: GVisor | Copy binaries
  copy:
    src: "{{ item.src }}"
    dest: "{{ bin_dir }}/{{ item.dest }}"
    mode: "0755"
    remote_src: true
  with_items:
    - { src: "{{ downloads.gvisor_runsc.dest }}", dest: "runsc" }
    - { src: "{{ downloads.gvisor_containerd_shim.dest }}", dest: "containerd-shim-runsc-v1" }
