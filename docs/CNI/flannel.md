# Flannel

Flannel is a network fabric for containers, designed for Kubernetes

Supported [backends](https://github.com/flannel-io/flannel/blob/master/Documentation/backends.md#wireguard): `vxlan`, `host-gw` and `wireguard`

**Warning:** You may encounter this [bug](https://github.com/coreos/flannel/pull/1282) with `VXLAN` backend, while waiting on a newer Flannel version the current workaround (`ethtool --offload flannel.1 rx off tx off`) is showcase in kubespray [networking test](tests/testcases/040_check-network-adv.yml:31).

## Verifying flannel install

* Flannel configuration file should have been created there

```ShellSession
cat /run/flannel/subnet.env
FLANNEL_NETWORK=**********/18
FLANNEL_SUBNET=***********/24
FLANNEL_MTU=1450
FLANNEL_IPMASQ=false
```

* Check if the network interface has been created

```ShellSession
ip a show dev flannel.1
4: flannel.1: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1450 qdisc noqueue state UNKNOWN group default
    link/ether e2:f3:a7:0f:bf:cb brd ff:ff:ff:ff:ff:ff
    inet ***********/18 scope global flannel.1
       valid_lft forever preferred_lft forever
    inet6 fe80::e0f3:a7ff:fe0f:bfcb/64 scope link
       valid_lft forever preferred_lft forever
```

* Try to run a container and check its ip address

```ShellSession
kubectl run test --image=busybox --command -- tail -f /dev/null
replicationcontroller "test" created

kubectl describe po test-34ozs | grep ^IP
IP:                             ***********
```

```ShellSession
kubectl exec test-34ozs -- ip a show dev eth0
8: eth0@if9: <BROADCAST,MULTICAST,UP,LOWER_UP,M-DOWN> mtu 1450 qdisc noqueue
    link/ether 02:42:0a:e9:2b:03 brd ff:ff:ff:ff:ff:ff
    inet ***********/24 scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::42:aff:fee9:2b03/64 scope link tentative flags 08
       valid_lft forever preferred_lft forever
```
