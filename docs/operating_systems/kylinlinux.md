# Kylin Linux

Kylin Linux is supported with docker and containerd runtimes.

**Note:** that Kylin Linux is not currently covered in kubespray CI and
support for it is currently considered experimental.

At present, only `Kylin Linux Advanced Server V10 (Sword)` has been adapted, which can support the deployment of aarch64 and x86_64 platforms.

There are no special considerations for using Kylin Linux as the target OS
for Kubespray deployments.
