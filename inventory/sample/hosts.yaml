all:
  hosts:
    master1:
      ansible_host: ************
      ip: ************
      access_ip: ************
    ecs-18-13:
      ansible_host: *************
      ip: *************
      access_ip: *************
      node_labels: '{"torin-system-scheduling":"true","torin-monitoring-scheduling":"true"}'
    ximu-03:
      ansible_host: **********
      ip: **********
      access_ip: **********
    ximu-05:
      ansible_host: **********
      ip: **********
      access_ip: **********
    biren-33:
      ansible_host: ***********
      ip: ***********
      access_ip: ***********
    biren-34:
      ansible_host: ***********
      ip: ***********
      access_ip: ***********

  vars:
    ansible_user: root
    ansible_ssh_private_key_file: /data/selfhost-k8s-admin.pem

  children:
    kube_control_plane:
      hosts:
        master1:
    etcd:
      hosts:
        master1:
        ecs-18-13:
        ximu-03:
    kube_node:
      hosts:
        ecs-18-13:
        ximu-03:
        ximu-05:
        biren-33:
        biren-34:
    
    # 路由反射器配置（关键）
    calico_rr:
      hosts:
        master1:          # 主RR，位于分区1
        ecs-18-13:        # 备用RR，位于分区1
    
    # 网络分区定义
    zone1:
      hosts:
        master1:
        ecs-18-13:
      vars:
        cluster_id: "*******"
        calico_as_num: "64512"
    
    zone2:
      hosts:
        ximu-03:
        ximu-05:
      vars:
        cluster_id: "*******" 
        calico_as_num: "64513"
    
    zone3:
      hosts:
        biren-33:
        biren-34:
      vars:
        cluster_id: "*******"
        calico_as_num: "64514"

    k8s_cluster:
      children:
        kube_control_plane:
        kube_node:
        calico_rr: