---
# Calico网络配置 - 多分区集群

# ===========================================
# 基础Calico配置
# ===========================================

# CNI插件名称
calico_cni_name: k8s-pod-network

# 使用Kubernetes作为数据存储
calico_datastore: "kdd"

# 网络后端选择VXLAN（适合跨网段通信）
calico_network_backend: vxlan

# ===========================================
# 封装模式配置（关键）
# ===========================================

# 禁用IP-in-IP，使用VXLAN
calico_ipip_mode: 'Never'

# 启用VXLAN封装，CrossSubnet模式（跨子网时启用封装）
calico_vxlan_mode: 'CrossSubnet'

# VXLAN配置
calico_vxlan_vni: 4096
calico_vxlan_port: 4789

# ===========================================
# IP池配置
# ===========================================

# 默认IP池配置
calico_pool_blocksize: 26
calico_pool_cidr: ***********/18  # 主IP池，覆盖所有Pod

# ===========================================
# BGP配置（重要）
# ===========================================

# 全局AS号
global_as_num: "64512"

# 启用与路由器对等（如果需要）
peer_with_router: false

# 出站NAT
nat_outgoing: true
nat_outgoing_ipv6: false

# ===========================================
# 路由反射器配置
# ===========================================

# BGP路由反射器配置将在inventory中定义

# ===========================================
# 性能和扩展性配置
# ===========================================

# 使用Typha提高扩展性（节点数>50时推荐）
typha_enabled: true
typha_replicas: 2  # 根据节点数调整，一般1个replica支持100个节点

# Typha安全连接
typha_secure: true

# ===========================================
# Felix配置
# ===========================================

# iptables后端
calico_iptables_backend: "Auto"

# 链插入模式
calico_felix_chaininsertmode: Insert

# 健康检查配置
calico_healthhost: "0.0.0.0"

# 探针超时设置
calico_node_livenessprobe_timeout: 10
calico_node_readinessprobe_timeout: 10

# ===========================================
# MTU配置
# ===========================================

# 网络接口MTU（VXLAN需要减去50字节）
calico_veth_mtu: 1450  # 假设网络MTU是1500

# ===========================================
# 安全和加密
# ===========================================

# 如需要，可启用Wireguard加密
# calico_wireguard_enabled: false

# ===========================================
# 高级配置
# ===========================================

# 服务广告（如果需要BGP广告服务IP）
# calico_advertise_cluster_ips: true

# 端点到主机的默认动作
calico_endpoint_to_host_action: "RETURN"

# IP自动检测方法
calico_ip_auto_method: "can-reach=*******"

# Felix额外环境变量
calico_node_extra_envs:
  FELIX_LOGSEVERITYSCREEN: "Info"
  FELIX_DEFAULTENDPOINTTOHOSTACTION: "RETURN"