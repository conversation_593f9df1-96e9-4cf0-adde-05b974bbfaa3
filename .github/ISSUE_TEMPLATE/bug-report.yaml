---
name: Bug Report
description: Report a bug encountered while using Kubespray
labels: kind/bug
body:
  - type: markdown
    attributes:
      value: |
        Please, be ready for followup questions, and please respond in a timely
        manner.  If we can't reproduce a bug or think a feature already exists, we
        might close your issue.  If we're wrong, PLEASE feel free to reopen it and
        explain why.
  - type: textarea
    id: problem
    attributes:
      label: What happened?
      description: |
        Please provide as much info as possible. Not doing so may result in your bug not being addressed in a timely manner.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What did you expect to happen?
    validations:
      required: true

  - type: textarea
    id: repro
    attributes:
      label: How can we reproduce it (as minimally and precisely as possible)?
    validations:
      required: true

  - type: markdown
    attributes:
      value: '### Environment'

  - type: dropdown
    id: os
    attributes:
      label: OS
      options:
        - 'RHEL 9'
        - 'RHEL 8'
        - 'Fedora 40'
        - 'Ubuntu 24'
        - 'Ubuntu 22'
        - 'Ubuntu 20'
        - 'Debian 12'
        - 'Debian 11'
        - 'Flatcar Container Linux'
        - 'openSUSE Leap'
        - 'openSUSE Tumbleweed'
        - 'Oracle Linux 9'
        - 'Oracle Linux 8'
        - 'AlmaLinux 9'
        - 'AlmaLinux 8'
        - 'Rocky Linux 9'
        - 'Rocky Linux 8'
        - 'Amazon Linux 2'
        - 'Kylin Linux Advanced Server V10'
        - 'UOS Linux 20'
        - 'openEuler 24'
        - 'openEuler 22'
        - 'openEuler 20'
        - 'Other|Unsupported'
    validations:
      required: true

  - type: textarea
    id: ansible_version
    attributes:
      label: Version of Ansible
      placeholder: 'ansible --version'
    validations:
      required: true

  - type: input
    id: python_version
    attributes:
      label: Version of Python
      placeholder: 'python --version'
    validations:
      required: true

  - type: input
    id: kubespray_version
    attributes:
      label: Version of Kubespray (commit)
      placeholder: 'git rev-parse --short HEAD'
    validations:
      required: true

  - type: dropdown
    id: network_plugin
    attributes:
      label: Network plugin used
      options:
        - calico
        - cilium
        - cni
        - custom_cni
        - flannel
        - kube-ovn
        - kube-router
        - macvlan
        - meta
        - multus
        - ovn4nfv
    validations:
      required: true

  - type: textarea
    id: inventory
    attributes:
      label: Full inventory with variables
      placeholder: 'ansible -i inventory/sample/inventory.ini all -m debug -a "var=hostvars[inventory_hostname]"'
      description: We recommend using snippets services like https://gist.github.com/ etc.
    validations:
      required: true

  - type: input
    id: ansible_command
    attributes:
      label: Command used to invoke ansible
    validations:
      required: true

  - type: textarea
    id: ansible_output
    attributes:
      label: Output of ansible run
      description: We recommend using snippets services like https://gist.github.com/ etc.
    validations:
      required: true

  - type: textarea
    id: anything_else
    attributes:
      label: Anything else we need to know
      description: |
        By running scripts/collect-info.yaml you can get a lot of useful informations.
        Script can be started by:
        ansible-playbook -i <inventory_file_path> -u <ssh_user> -e ansible_ssh_user=<ssh_user> -b --become-user=root -e dir=`pwd` scripts/collect-info.yaml
        (If you using CoreOS remember to add '-e ansible_python_interpreter=/opt/bin/python').
        After running this command you can find logs in `pwd`/logs.tar.gz. You can even upload somewhere entire file and paste link here
