name: Issue labeler
on:
  issues:
    types: [opened]

permissions:
  contents: read

jobs:
  label-component:
    runs-on: ubuntu-latest
    permissions:
      issues: write

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Parse issue form
        uses: stefanbuck/github-issue-parser@2ea9b35a8c584529ed00891a8f7e41dc46d0441e
        id: issue-parser
        with:
          template-path: .github/ISSUE_TEMPLATE/bug-report.yaml

      - name: Set labels based on OS field
        uses: redhat-plumbers-in-action/advanced-issue-labeler@0db433d412193574252480b4fc22f2e4319a4ea3
        with:
          issue-form: ${{ steps.issue-parser.outputs.jsonString }}
          section: os
          block-list: |
            None
            Other
          token: ${{ secrets.GITHUB_TOKEN }}
