---
vagrant:
  extends: .job-moderated
  variables:
    CI_PLATFORM: "vagrant"
    SSH_USER: "vagrant"
    VAGRANT_DEFAULT_PROVIDER: "libvirt"
    KUBESPRAY_VAGRANT_CONFIG: tests/files/${TESTCASE}.rb
    DOCKER_NAME: vagrant
    VAGRANT_ANSIBLE_TAGS: facts
    VAGRANT_HOME: "$CI_PROJECT_DIR/.vagrant.d"
    PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  tags: [ffci-vm-large]
  image: quay.io/kubespray/vm-kubespray-ci:v13
  services: []
  before_script:
    - echo $USER
    - python3 -m venv citest
    - source citest/bin/activate
    - vagrant plugin expunge --reinstall --force --no-tty
    - vagrant plugin install vagrant-libvirt
    - pip install --no-compile --no-cache-dir pip -U
    - pip install --no-compile --no-cache-dir -r $CI_PROJECT_DIR/requirements.txt
    - pip install --no-compile --no-cache-dir -r $CI_PROJECT_DIR/tests/requirements.txt
    - ./tests/scripts/vagrant_clean.sh
  script:
    - vagrant up
    - ./tests/scripts/testcases_run.sh
  after_script:
    - vagrant destroy -f
  cache:
    key: $CI_JOB_NAME_SLUG
    paths:
      - .vagrant.d/boxes
      - .cache/pip
    policy: pull-push # TODO: change to "pull" when not on main
  stage: deploy-extended
  rules:
    - if: $PR_LABELS =~ /.*(ci-extended|ci-full).*/
      when: on_success
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_PIPELINE_SCHEDULE_DESCRIPTION == "daily-ci"
      when: on_success
    - when: manual
      allow_failure: true
  parallel:
    matrix:
      - TESTCASE:
          - ubuntu24-calico-dual-stack
          - ubuntu24-calico-ipv6only-stack
