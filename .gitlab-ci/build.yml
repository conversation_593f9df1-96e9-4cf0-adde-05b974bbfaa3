---
pipeline-image:
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - image-cache
  tags:
    - ffci
  stage: build
  image: moby/buildkit:rootless
  variables:
    BUILDKITD_FLAGS: --oci-worker-no-process-sandbox
    CACHE_IMAGE: $CI_REGISTRY_IMAGE/pipeline:cache
  # TODO: remove the override
  # currently rebase.sh depends on bash (not available in the kaniko image)
  # once we have a simpler rebase (which should be easy if the target branch ref is available as variable
  # we'll be able to rebase here as well hopefully
  before_script:
    - mkdir -p ~/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n ${CI_REGISTRY_USER}:${CI_REGISTRY_PASSWORD} | base64)\"}}}" > ~/.docker/config.json
  script:
    - |
      buildctl-daemonless.sh build \
        --frontend dockerfile.v0 \
        --local context=$CI_PROJECT_DIR \
        --local dockerfile=$CI_PROJECT_DIR \
        --opt filename=pipeline.Dockerfile \
        --export-cache type=registry,ref=$CACHE_IMAGE \
        --import-cache type=registry,ref=$CACHE_IMAGE \
        --output type=image,name=$PIPELINE_IMAGE,push=true
