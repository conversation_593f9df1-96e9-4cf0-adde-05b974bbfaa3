---
pre-commit:
  stage: test
  tags:
  - ffci
  image: 'ghcr.io/pre-commit-ci/runner-image@sha256:fe01a6ec51b298412990b88627c3973b1146c7304f930f469bafa29ba60bcde9'
  variables:
    PRE_COMMIT_HOME: ${CI_PROJECT_DIR}/.cache/pre-commit
    ANSIBLE_STDOUT_CALLBACK: default
  script:
  - pre-commit run --all-files --show-diff-on-failure
  cache:
    key: pre-commit-2
    paths:
    - ${PRE_COMMIT_HOME}
    when: 'always'
  needs: []

vagrant-validate:
  extends: .job
  stage: test
  tags: [ffci]
  variables:
    VAGRANT_VERSION: 2.3.7
  script:
  - ./tests/scripts/vagrant-validate.sh
