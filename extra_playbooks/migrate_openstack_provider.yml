---
- name: Remove old cloud provider config
  hosts: kube_node:kube_control_plane
  tasks:
    - name: Remove old cloud provider config
      file:
        path: "{{ item }}"
        state: absent
      with_items:
        - /etc/kubernetes/cloud_config
- name: Migrate intree Cinder PV
  hosts: kube_control_plane[0]
  tasks:
    - name: Include kubespray-default variables
      include_vars: ../roles/kubespray_defaults/defaults/main/main.yml
    - name: Copy get_cinder_pvs.sh to first control plane node
      copy:
        src: get_cinder_pvs.sh
        dest: /tmp
        mode: u+rwx
    - name: Get PVs provisioned by in-tree cloud provider
      command: /tmp/get_cinder_pvs.sh
      register: pvs
    - name: Remove get_cinder_pvs.sh
      file:
        path: /tmp/get_cinder_pvs.sh
        state: absent
    - name: Rewrite the "pv.kubernetes.io/provisioned-by" annotation
      command: "{{ bin_dir }}/kubectl annotate --overwrite pv {{ item }} pv.kubernetes.io/provisioned-by=cinder.csi.openstack.org"
      loop: "{{ pvs.stdout_lines | list }}"
